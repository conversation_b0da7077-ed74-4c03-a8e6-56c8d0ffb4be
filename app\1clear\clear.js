// @ts-check

/**
 * Subtitle Pre-processing and Translation Pipeline
 *
 * This script performs the following tasks:
 *
 * 1. Processes .mkv files in a specified directory to extract and prepare subtitles for translation.
 * 2. Extracts subtitle tracks (English and French) from .mkv files using FFmpeg for enhanced translation.
 * 3. Cleans and formats the extracted .ass subtitle files, preserving only dialogue lines.
 * 4. <PERSON>ames processed files for consistency and to fix known title issues.
 * 5. Outputs cleaned subtitle content to .txt files for further processing.
 * 6. Implements robust error handling and logging mechanisms:
 *    - Logs information, errors, uncaught exceptions, and unhandled rejections to separate files.
 *    - Sends critical error notifications to a Discord webhook for monitoring.
 * 7. Utilizes color-coded console output for better readability and error distinction.
 * 8. Manages file operations for creating necessary directories and handling file streams.
 * 9. Implements a graceful shutdown process to ensure proper resource cleanup.
 * 10. Uses environment variables for configuration (e.g., Discord webhook URL).
 * 11. Supports multi-language subtitle extraction (English + French for second language validation).
 * 12. Prepares subtitle data for the enhanced tool-based translation process.
 * 13. Initiates the enhanced translation script with second language validation support.
 * 14. Handles various edge cases in filename parsing and subtitle formatting.
 * 15. Provides detailed logging and error reporting throughout the process.
 *
 * The script is designed as part of a larger pipeline for automating anime subtitle
 * translation, focusing on the pre-processing steps to prepare subtitle data for
 * machine translation or other processing steps. It ensures efficient operation,
 * error recovery, and detailed logging for troubleshooting.
 */

import { config } from 'dotenv';
config();
import path from 'path';
import axios from 'axios';
import fs from 'fs';
import * as cp from 'child_process';
import fetch from 'node-fetch';
import { promisify } from 'util';
const execAsync = promisify(cp.exec);

const IS_RUNNING_FILE = 'isRunning.txt';

/**
 * @constant {Object} COLORS - ANSI color codes for console output.
 */
const COLORS = {
  RESET: '\x1b[0m',
  BLACK: '\x1b[30m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  DIM: '\x1b[2m',
  BG_BLACK: '\x1b[40m',
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
  BG_WHITE: '\x1b[47m',
};

const HF_API_TOKEN = process.env.HF_API_TOKEN; // Move token to .env file for security

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

const convertMkvToWav = async (mkvPath, outputPath) => {
  try {
    await execAsync(`ffmpeg -i "${mkvPath}" -vn -acodec pcm_s16le "${outputPath}" -y`);
  } catch (error) {
    throw new Error(`Failed to convert MKV to WAV: ${error.message}`);
  }
};

const createSafeFilename = (timestamp) => {
  return timestamp.replace(/:/g, '.');
};

const splitAudioSegment = async (inputPath, start, end, outputPath) => {
  try {
    await execAsync(`ffmpeg -i "${inputPath}" -ss ${start} -to ${end} -acodec copy "${outputPath}" -loglevel warning -stats -y`);
  } catch (error) {
    throw new Error(`Failed to split audio segment: ${error.message}`);
  }
};

const predictGender = async (wavFilePath, retries = 10) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const audioData = await fs.promises.readFile(wavFilePath);
      const response = await fetch(
        // @ts-ignore
        process.env.HUGGINGFACE_ENDPOINT,
        {
          headers: {
            Authorization: `Bearer ${HF_API_TOKEN}`,
            "Content-Type": "audio/wav",
          },
          method: "POST",
          body: audioData,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (attempt === retries) {
        console.error(`Final attempt ${attempt}/${retries} failed for ${wavFilePath}: ${error.message}`);
        return null;
      }
      console.log(`Attempt ${attempt}/${retries} failed for ${wavFilePath}: ${error.message}. Retrying...`);
      await delay(10000 * attempt);
    }
  }
};

const logsDir = 'logs';
createDirectoryIfNotExists(logsDir);

const infoLogStream = createWriteStream(path.join(logsDir, 'info.log'));
const errorLogStream = createWriteStream(path.join(logsDir, 'error.log'));
const exceptionLogStream = createWriteStream(path.join(logsDir, 'exception.log'));
const rejectionLogStream = createWriteStream(path.join(logsDir, 'rejection.log'));

setupLogging();
setupUncaughtErrorHandling();

/* Begin script here */
main();

/**
 * Main function to initiate the process.
 * gahter all .mkv files and processs them with processFile function
 * @returns {Promise<void>}
 */
async function main() {
  try {
    // Process all .mkv files in the downloads directory
    const extractedDir = '0rss/downloads';
    const files = fs.readdirSync(extractedDir).filter((file) => file.endsWith('.mkv'));

    for (const file of files) {
      const filePath = path.join(extractedDir, file);
      await processFile(filePath);
      console.info(`${COLORS.WHITE}${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Processed file: ${file}${COLORS.RESET}`);
    }

    // After processing all files, initiate the translation step
    console.info(
      `${COLORS.WHITE}${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] All files processed. Proceeding to the <TRANSLATING> step...${COLORS.RESET}`
    );
    startTranslatingProcess();
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] An error occurred during the script execution: ${COLORS.RESET}`, error);
    process.exit(1);
  }
}

/**
 * Processes a single .mkv file by extracting subtitles and clearing the .ass file.
 * clearing involves exctracting dialogue lines from the .ass file and removing any other thigns such as timigs etc.
 * so that a line
 * Dialogue: 0,0:00:50.27,0:00:52.02,main,Rimuru,0,0,0,,Oczywiście, że nie.
 * will become
 * Oczywiście, że nie.
 * This is essential becuase later in 2applyTranslation we only change the dialogue the be polish, and leave the timigs etc. as they are
 * It's also way more cost-effective and performat than shoving unfiltered data to an AI model.
 * @param {string} filePath - Path to the .mkv file.
 * @returns {Promise<void>}
 */
async function processFile(filePath) {
  const filename = path.basename(filePath);
  if (path.extname(filename) !== '.mkv') {
    console.error(`${COLORS.RED}[ERROR] The current file is not an .mkv file: ${filename}${COLORS.RESET}`);
    return;
  }

  // Extract anime title and episode number from the filename
  const mkvFile = filename;

  // Check if this is a ToonsHub file
  const isToonsHub = mkvFile.includes('ToonsHub');

  let animeTitle, animeEpisodeNumber, finalAnimeTitle, finalAnimeEpisodeNumber;

  if (isToonsHub) {
    // Handle ToonsHub filename format
    // Example: The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Other.Demon.Sword.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub.mkv
    const toonsHubMatch = mkvFile.match(/(.+?)\.S(\d+)E(\d+)\..+?-ToonsHub\.mkv$/);
    if (!toonsHubMatch) {
      console.error(`${COLORS.RED}[ERROR] ToonsHub title match failed for: ${mkvFile}${COLORS.RESET}`);
      return;
    }

    // Convert dots to spaces in the title
    animeTitle = toonsHubMatch[1].replace(/\./g, ' ');
    const season = toonsHubMatch[2];
    animeEpisodeNumber = toonsHubMatch[3].padStart(2, '0'); // Ensure 2 digits

    // For ToonsHub files, we'll use the extracted title and episode number directly
    finalAnimeTitle = animeTitle;
    finalAnimeEpisodeNumber = animeEpisodeNumber;

    console.info(`${COLORS.CYAN}[INFO] Extracted from ToonsHub file: Title="${animeTitle}", Episode=${animeEpisodeNumber}${COLORS.RESET}`);
  } else {
    // Handle Erai-raws filename format
    const titleMatch = mkvFile.match(/\]\s(.*?)\s-\s(\d+)/);
    if (!titleMatch) {
      console.error(`${COLORS.RED}[ERROR] Erai-raws title match failed for: ${mkvFile}${COLORS.RESET}`);
      return;
    }
    animeTitle = titleMatch[1];
    animeEpisodeNumber = titleMatch[2];

    // Standardize the filename format
    const fullTitle = `[Erai-raws] ${animeTitle} - ${animeEpisodeNumber} [1080p]`;
    const finalTilteMatch = fullTitle.match(/\]\s(.*?)\s-\s(\d+)/);
    if (!finalTilteMatch) {
      throw Error('Failed to match the title!');
    }
    finalAnimeTitle = finalTilteMatch[1];
    finalAnimeEpisodeNumber = finalTilteMatch[2];
  }

  // Rename the file to ensure consistency
  // const oldFilename = mkvFile;
  // let newMkvFile = mkvFile
  //   .replace(animeTitle, finalAnimeTitle)
  //   .replace(animeEpisodeNumber, finalAnimeEpisodeNumber)
  //   .replace(/-(?= [a-zA-Z])/gi, '');
  // fs.renameSync(`0rss/downloads/${oldFilename}`, `0rss/downloads/${newMkvFile}`);
  let newMkvFile = mkvFile

  // Set up paths for subtitle extraction
  const inputPath = `0rss/downloads/${newMkvFile}`;
  const outputPathEng = `1clear/extracted/${newMkvFile.slice(0, -4)}_eng.ass`;
  const outputPathOther = `1clear/extracted/${newMkvFile.slice(0, -4)}_other.ass`;


  try {
    // Extract English subtitles
    await extractSubtitles(inputPath, outputPathEng, 'eng');

    // Extract French subtitles (for second language validation)
    console.info(`${COLORS.CYAN}[INFO] Attempting to extract French subtitles for second language validation...${COLORS.RESET}`);
    try {
      await extractSubtitles(inputPath, outputPathOther, 'fre');
      console.info(`${COLORS.GREEN}[INFO] French subtitles extracted successfully${COLORS.RESET}`);
    } catch (frenchError) {
      console.warn(`${COLORS.YELLOW}[WARN] Failed to extract French subtitles: ${frenchError.message}${COLORS.RESET}`);
      console.info(`${COLORS.GRAY}[INFO] Continuing without French subtitles - second language validation will be skipped${COLORS.RESET}`);
    }

    // Check first 10 dialogue lines in the subtitle file for missing/invalid actors
    const needsGenderPrediction = await checkIfNeedsGenderPrediction(outputPathEng);

    let predictions = [];
    if (needsGenderPrediction) {
      // Only process audio if needed
      console.info(`${COLORS.GRAY}[INFO] Missing or invalid actors detected. Processing audio for gender prediction...${COLORS.RESET}`);

      const outputDir = path.join('audio_processing', path.basename(filePath, '.mkv'));
      createDirectoryIfNotExists(outputDir);

      const fullWavPath = path.join(outputDir, 'full.wav');
      await convertMkvToWav(filePath, fullWavPath);

      const subtitleContent = await fs.promises.readFile(outputPathEng, 'utf-8');
      const dialogues = extractDialogueTimestamps(subtitleContent);

      predictions = await processDialogueSegments(fullWavPath, dialogues, outputDir);

      // Clean up
      await fs.promises.unlink(fullWavPath);
    } else {
      // console.info(`${COLORS.GRAY}[INFO] All actors properly defined in first 10 lines. Skipping audio processing.${COLORS.RESET}`);
    }

    // Continue with subtitle clearing for English
    await clearAssFile(outputPathEng, 'eng', predictions);

    // Clear French subtitles if they exist
    if (fs.existsSync(outputPathOther)) {
      console.info(`${COLORS.CYAN}[INFO] Processing French subtitles for second language validation...${COLORS.RESET}`);
      await clearAssFile(outputPathOther, 'other', predictions);
    }

  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Failed to process file: ${filePath}${COLORS.RESET}`, error);
    throw error;
  }
}

async function checkIfNeedsGenderPrediction(subtitlePath) {
  const content = await fs.promises.readFile(subtitlePath, 'utf-8');
  const lines = content.split('\n');
  let dialogueCount = 0;

  for (const line of lines) {
    if (line.startsWith('Dialogue:')) {
      dialogueCount++;
      const parts = line.split(',');
      const actor = parts[4];

      // Check if actor is missing, NTP, or OS
      if (!actor || actor === '' || actor === 'NTP' || actor === 'OS') {
        return true;
      }

      // Only check first 10 dialogue lines
      if (dialogueCount >= 10) {
        break;
      }
    }
  }

  return false;
}

function extractDialogueTimestamps(subtitleContent) {
  const lines = subtitleContent.split('\n');
  const dialogues = [];

  for (const line of lines) {
    if (line.startsWith('Dialogue:')) {
      const parts = line.split(',');
      dialogues.push({
        start: parts[1].trim(),
        end: parts[2].trim()
      });
    }
  }

  return dialogues;
}

async function processDialogueSegments(fullWavPath, dialogues, outputDir) {
  const predictions = [];
  const segmentPaths = [];

  // Create all segments in parallel using Promise.all
  console.info(`${COLORS.GRAY}[INFO] Creating ${dialogues.length} audio segments in parallel...${COLORS.RESET}`);
  const segmentCreationPromises = dialogues.map(async dialogue => {
    const segmentPath = path.join(outputDir,
      `segment_${createSafeFilename(dialogue.start)}-${createSafeFilename(dialogue.end)}.wav`);

    try {
      await splitAudioSegment(fullWavPath, dialogue.start, dialogue.end, segmentPath);
      segmentPaths.push({
        path: segmentPath,
        start: dialogue.start,
        end: dialogue.end
      });
      console.info(`${COLORS.GRAY}[INFO] Created segment: ${path.basename(segmentPath)}${COLORS.RESET}`);
    } catch (error) {
      console.error(`${COLORS.RED}[ERROR] Failed to create segment for ${dialogue.start}-${dialogue.end}: ${error.message}${COLORS.RESET}`);
    }
  });

  await Promise.all(segmentCreationPromises);
  console.info(`${COLORS.GRAY}[INFO] Created ${segmentPaths.length} segments successfully${COLORS.RESET}`);

  // Then process all segments for gender prediction
  console.info(`${COLORS.GRAY}[INFO] Processing gender predictions for ${segmentPaths.length} segments...${COLORS.RESET}`);
  for (const segment of segmentPaths) {
    try {
      const prediction = await predictGender(segment.path);
      if (prediction) {
        predictions.push({
          timestamp: `${segment.start} - ${segment.end}`,
          prediction: prediction
        });
        console.info(`${COLORS.GRAY}[INFO] Processed prediction for: ${path.basename(segment.path)}${COLORS.RESET}`);
      }
    } catch (error) {
      console.error(`${COLORS.RED}[ERROR] Failed to process prediction for ${segment.path}: ${error.message}${COLORS.RESET}`);
    }
  }

  // Finally, clean up all segments
  console.info(`${COLORS.GRAY}[INFO] Cleaning up segments...${COLORS.RESET}`);
  await Promise.all(segmentPaths.map(segment =>
    fs.promises.unlink(segment.path).catch(error =>
      console.error(`${COLORS.RED}[ERROR] Failed to delete segment ${segment.path}: ${error.message}${COLORS.RESET}`)
    )
  ));

  return predictions;
}

/**
 * Extracts subtitles from an .mkv file using FFmpeg.
 * @param {string} inputPath - The path to the input .mkv file.
 * @param {string} outputPath - The path to save the extracted .ass file.
 * @param {string} language - The language code ('eng' or 'other').
 * @returns {Promise<void>}
 * @throws {Error} If there is an error during the FFmpeg process.
 */
async function extractSubtitles(inputPath, outputPath, language) {
  console.info(`${COLORS.GRAY}[INFO] Extracting ${language} subtitles from: ${inputPath}...${COLORS.RESET}`);

  // Check if this is a ToonsHub file
  const isToonsHub = inputPath.includes('ToonsHub');

  return new Promise((resolve, reject) => {
    // Construct FFmpeg command to extract subtitles
    let ffmpegArgs;

    if (isToonsHub) {
      // For ToonsHub files, we'll try different approaches to extract subtitles
      if (language === 'eng') {
        // First try: Use the first subtitle track for English
        ffmpegArgs = ['-i', `"${inputPath}"`, '-map', '0:s:0', '-y', `"${outputPath}"`];
        console.info(`${COLORS.CYAN}[INFO] Using first subtitle track for ToonsHub file (English)${COLORS.RESET}`);
      } else if (language === 'fre') {
        // For French, try the second subtitle track
        ffmpegArgs = ['-i', `"${inputPath}"`, '-map', '0:s:1', '-y', `"${outputPath}"`];
        console.info(`${COLORS.CYAN}[INFO] Using second subtitle track for ToonsHub file (French)${COLORS.RESET}`);
      } else {
        // Fallback for other languages
        ffmpegArgs = ['-i', `"${inputPath}"`, '-map', '0:s:0', '-y', `"${outputPath}"`];
        console.info(`${COLORS.CYAN}[INFO] Using first subtitle track for ToonsHub file (${language})${COLORS.RESET}`);
      }
    } else {
      // For Erai-raws files, use language mapping as before
      ffmpegArgs = ['-i', `"${inputPath}"`, '-map', `0:s:m:language:${language}`, '-y', `"${outputPath}"`];
    }

    console.log(`ffmpeg ${ffmpegArgs.join(' ')}`);

    // Execute FFmpeg command
    const ffmpegProcess = cp.spawn('ffmpeg', ffmpegArgs, { windowsHide: true, shell: true });

    let stderr = '';
    ffmpegProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    ffmpegProcess.on('error', (err) => {
      reject(new Error(`${COLORS.RED}[ERROR] FFmpeg process error: ${COLORS.RESET}\n${err.message}`));
    });

    ffmpegProcess.on('exit', (code) => {
      if (code === 0) {
        resolve();
      } else {
        // If the first attempt failed for ToonsHub files, try a different approach
        if (isToonsHub && stderr.includes('Stream map')) {
          console.info(`${COLORS.YELLOW}[INFO] First attempt failed, trying alternative subtitle extraction for ToonsHub file${COLORS.RESET}`);

          let alternativeArgs;
          if (language === 'fre') {
            // For French, try extracting all subtitle tracks and hope one is French
            alternativeArgs = ['-i', `"${inputPath}"`, '-map', '0:s', '-y', `"${outputPath}"`];
            console.info(`${COLORS.YELLOW}[INFO] Trying to extract all subtitle tracks for French${COLORS.RESET}`);
          } else {
            // For English, try the standard fallback
            alternativeArgs = ['-i', `"${inputPath}"`, '-map', '0:s', '-y', `"${outputPath}"`];
          }

          console.log(`ffmpeg ${alternativeArgs.join(' ')}`);

          const alternativeProcess = cp.spawn('ffmpeg', alternativeArgs, { windowsHide: true, shell: true });

          alternativeProcess.on('error', (err) => {
            reject(new Error(`${COLORS.RED}[ERROR] FFmpeg alternative process error: ${COLORS.RESET}\n${err.message}`));
          });

          alternativeProcess.on('exit', (altCode) => {
            if (altCode === 0) {
              resolve();
            } else {
              reject(new Error(`${COLORS.RED}[ERROR] FFmpeg alternative error: ${COLORS.RESET}\nProcess exited with code ${altCode}`));
            }
          });
        } else {
          reject(new Error(`${COLORS.RED}[ERROR] FFmpeg error: ${COLORS.RESET}\nProcess exited with code ${code}\n${stderr}`));
        }
      }
    });
  });
}

/**
 * Clears the .ass file by removing unnecessary lines and formatting the dialogue.
 * Only preserve dialogue lines, cut anything else like style declarations, metadata etc..
 * @param {string} filePath - The path to the .ass file.
 * @param {string} language - The language code ('eng' or 'other').
 * @returns {Promise<void>}
 */
/**
 * Clears the .ass file by removing unnecessary lines and formatting the dialogue.
 * Uses gender predictions when actor information is missing.
 * @param {string} filePath - The path to the .ass file.
 * @param {string} language - The language code ('eng' or 'other').
 * @returns {Promise<void>}
 */
async function clearAssFile(filePath, language, predictions = []) {
  console.info(`${COLORS.GRAY}[INFO] Clearing ${language} ass file: ${filePath}...${COLORS.RESET}`);

  // Read the .ass file
  const data = fs.readFileSync(filePath, 'utf-8');

  // Read gender predictions file from the same directory as clear.js
  const scriptDir = path.dirname(new URL(import.meta.url).pathname);
  const decodedPath = decodeURIComponent(scriptDir).replace(/^\//, ''); // Remove leading slash for Windows
  const genderPredictionsPath = path.join(decodedPath, 'gender_predictions.json');
  let genderPredictions = predictions;

  // If no predictions were passed, try to load them from file
  if (!predictions.length && fs.existsSync(genderPredictionsPath)) {
    genderPredictions = JSON.parse(fs.readFileSync(genderPredictionsPath, 'utf-8'));
  } else if (!predictions.length) {
    // console.warn(`${COLORS.YELLOW}[WARNING] No gender predictions available${COLORS.RESET}`);
  }

  // Helper function to get actor based on timestamp and gender predictions
  function getActorFromPredictions(startTime, endTime) {
    const prediction = genderPredictions.find(pred => {
      const [predStart, predEnd] = pred.timestamp.split(' - ');
      return predStart === startTime && predEnd === endTime;
    });

    if (!prediction) return 'Person';

    // Handle the new JSON format where prediction is an array with simpler objects
    const gender = prediction.prediction[0]?.gender;
    return gender === 'female' ? 'Female' : 'Male';
  }

  // Process the file content
  const output = data
    .split('\n')
    .filter((line) => {
      if (line.startsWith('Dialogue')) {
        const parts = line.split(',');
        const dialogue = parts[9]?.trim();
        return dialogue && dialogue !== '';
      }
      return false;
    })
    .map((line) => {
      const parts = line.split(',');
      let actor = parts[4];
      const startTime = parts[1];
      const endTime = parts[2];
      const dialogue = parts.slice(9).join(',').trim();

      // Check if actor is missing, NTP, or OS
      if (!actor || actor === '' || actor === 'NTP' || actor === 'OS') {
        actor = getActorFromPredictions(startTime, endTime);
      }

      return `${actor} | ${dialogue}`;
    })
    .join('\n');

  // Write the processed content to a new file
  const outputDir = `2translate/toTranslate/${path.basename(filePath).replace('.ass', '')}.txt`;
  fs.writeFileSync(outputDir, output, 'utf-8');
  console.info(`${COLORS.CYAN}[INFO] Clearing completed for ${language} subtitles: ${filePath}${COLORS.RESET}`);
  sendDebugInfoToDiscord(`[INFO] Clearing completed for ${language} subtitles: ${filePath}`);
}

/**
 * Creates a directory if it doesn't already exist.
 * @param {string} directory - The directory path to create.
 * @returns {void}
 */
function createDirectoryIfNotExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory);
  }
}

/**
 * Creates a write stream for a file.
 * @param {string} filePath - The path to the file.
 * @returns {fs.WriteStream} The created write stream.
 */
function createWriteStream(filePath) {
  return fs.createWriteStream(filePath, { flags: 'a' });
}

/**
 * Overrides the console.info function to log messages to a file.
 * @param {fs.WriteStream} logStream - The write stream for the log file.
 * @returns {void}
 */
function overrideConsoleLog(logStream) {
  const originalLog = console.info;
  console.info = (...args) => {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] ${args.join(' ')}\n`;
    originalLog(...args);
    logStream.write(logMessage);
  };
}

/**
 * Overrides the console.error function to log error messages to a file.
 * @param {fs.WriteStream} errorStream - The write stream for the error log file.
 * @returns {void}
 */
function overrideConsoleError(errorStream) {
  const originalError = console.error;
  console.error = (...args) => {
    const timestamp = getTimestamp();
    const errorMessage = cleanErrorMessage(`[${timestamp}] ${args.join(' ')}\n`);
    originalError(...args);
    errorStream.write(errorMessage);
  };
}

/**
 * Handles uncaught exceptions by logging and sending debug information to Discord.

 * @param {Error} err - The uncaught exception error object.
 * @returns {Promise<void>}
 */
async function handleUncaughtException(err) {
  const errorMessage = cleanErrorMessage(err.toString());
  await sendDebugInfoToDiscord(`\`[ERROR]\` <@351006685587963916> \`\`\`${errorMessage}\n${err.stack}\`\`\``);
  const timestamp = getTimestamp();
  const exceptionMessage = `[${timestamp}] ${err.stack}\n`;
  console.error(exceptionMessage);
  exceptionLogStream.write(exceptionMessage);
  process.exit(1);
}

/**
 * Handles unhandled rejections by logging and sending debug information to Discord.
 * @param {*} reason - The reason for the unhandled rejection.
 * @param {Promise} promise - The promise that caused the unhandled rejection.
 * @returns {Promise<void>}
 */
async function handleUnhandledRejection(reason, promise) {
  const reasonMessage = cleanErrorMessage(reason.toString());
  await sendDebugInfoToDiscord(`\`[ERROR]\` <@351006685587963916> \`\`\`${reasonMessage}\n${reason.stack}\`\`\``);
  const timestamp = getTimestamp();
  const rejectionMessage = `[${timestamp}]\n${reason.stack}\n`;
  console.error(rejectionMessage);
  rejectionLogStream.write(rejectionMessage);
  process.exit(1);
}

/**
 * Handles graceful shutdown by stopping the script and closing log streams.
 * @returns {void}
 */
function handleGracefulShutdown() {
  stopRunning();
  const timestamp = getTimestamp();
  const shutdownMessage = `[${timestamp}] Graceful shutdown\n`;
  console.info(shutdownMessage);
  infoLogStream.write(shutdownMessage);
  infoLogStream.end();
  errorLogStream.end();
  exceptionLogStream.end();
  rejectionLogStream.end(() => {
    process.exit(0);
  });
}

/**
 * Stops the script by marking it as not running.
 * @returns {void}
 */
function stopRunning() {
  fs.writeFileSync(IS_RUNNING_FILE, 'false');
}

/**
 * Gets the current timestamp in ISO format.
 * @returns {string} The current timestamp.
 */
function getTimestamp() {
  const now = new Date();
  return now.toISOString();
}

/**
 * Cleans the error message by removing ANSI color codes.
 * @param {string} errorMessage - The error message to clean.
 * @returns {string} The cleaned error message.
 */
function cleanErrorMessage(errorMessage) {
  // @ts-ignore
  return (
    errorMessage
      // @ts-ignore
      .replaceAll(/\[90m/g, '')
      .replace(/\[36m/g, '')
      .replace(/\[37m/g, '')
      .replace(/\[46m/g, '')
      .replace(/\[30m/g, '')
      .replace(/\[0m/g, '')
  );
}

/**
 * Sends debug information to a Discord webhook.

 * @param {string} content - The content to send in the Discord message.
 * @returns {Promise<void>}
 */
async function sendDebugInfoToDiscord(content) {
  try {
    const webhookURL = process.env.DEV_DISCORD_WEBHOOK;
    if (!webhookURL) {
      console.error('Discord webhook URL is not defined in the environment variables.');
      return;
    }
    const payload = {
      content: content,
    };
    const response = await axios.post(webhookURL, payload);
    if (response.status !== 204) {
      console.error('Failed to send Discord webhook. Status code:', response.status);
    }
  } catch (error) {
    console.error(`${COLORS.RED}Error sending Discord webhook:${COLORS.RESET}\n${error.message}`);
  }
}

/**
 * Sets up uncaught error handling by attaching event listeners.
 * @returns {void}
 */
function setupUncaughtErrorHandling() {
  process.on('uncaughtException', handleUncaughtException);
  process.on('unhandledRejection', handleUnhandledRejection);
  process.on('SIGINT', handleGracefulShutdown);
}

/**
 * Sets up logging by overriding console.log and console.error functions.
 * @returns {void}
 */
function setupLogging() {
  overrideConsoleLog(infoLogStream);
  overrideConsoleError(errorLogStream);
}

function startTranslatingProcess() {
  try {
    console.info(`${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Starting translating process...${COLORS.RESET}`);
    cp.execSync('node 2translate/translate.js', { stdio: 'inherit' });
    console.info(`${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Translating process completed successfully.${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error in translating process: ${error.message}${COLORS.RESET}`);
    process.exit(1); // Exit the entire application if translation process fails
  }
}
